import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getProducts, getCategories, Product, Category } from '../lib/supabase';

export const useProducts = (categoryId?: string) => {
  return useQuery({
    queryKey: ['products', categoryId],
    queryFn: () => getProducts(categoryId),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useCategories = () => {
  return useQuery({
    queryKey: ['categories'],
    queryFn: getCategories,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

export const useProduct = (productId: string) => {
  return useQuery({
    queryKey: ['product', productId],
    queryFn: async () => {
      const { data, error } = await getProducts();
      if (error) throw error;
      return data?.find((product: Product) => product.id === productId);
    },
    enabled: !!productId,
  });
};
