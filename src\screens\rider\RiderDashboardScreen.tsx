import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Alert, Switch } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useAuth } from '../../contexts/AuthContext';
import { supabase } from '../../lib/supabase';
import { RootStackParamList } from '../../navigation/types';

type RiderDashboardNavigationProp = NativeStackNavigationProp<RootStackParamList, 'RiderDashboard'>;

interface RiderStats {
  totalDeliveries: number;
  todayDeliveries: number;
  pendingDeliveries: number;
  rating: number;
  earnings: number;
}

interface Order {
  id: string;
  total_amount: number;
  delivery_address: string;
  status: string;
  created_at: string;
  user_profiles?: {
    first_name?: string;
    last_name?: string;
    phone?: string;
  } | null;
}

const RiderDashboardScreen = () => {
  const navigation = useNavigation<RiderDashboardNavigationProp>();
  const { user, userProfile, signOut } = useAuth();
  const [isOnline, setIsOnline] = useState(false);
  const [stats, setStats] = useState<RiderStats>({
    totalDeliveries: 0,
    todayDeliveries: 0,
    pendingDeliveries: 0,
    rating: 5.0,
    earnings: 0,
  });
  const [availableOrders, setAvailableOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Redirect if not rider
    if (userProfile && userProfile.role !== 'rider') {
      Alert.alert('Access Denied', 'You do not have rider privileges.');
      navigation.goBack();
      return;
    }

    fetchRiderData();
    fetchAvailableOrders();
  }, [userProfile]);

  const fetchRiderData = async () => {
    try {
      if (!user) return;

      // Get rider info
      const { data: riderData } = await supabase
        .from('drivers')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (riderData) {
        setIsOnline(riderData.status === 'available');
        
        // Fetch rider stats
        const { count: totalDeliveries } = await supabase
          .from('orders')
          .select('*', { count: 'exact', head: true })
          .eq('driver_id', riderData.id)
          .eq('status', 'delivered');

        const today = new Date().toISOString().split('T')[0];
        const { count: todayDeliveries } = await supabase
          .from('orders')
          .select('*', { count: 'exact', head: true })
          .eq('driver_id', riderData.id)
          .eq('status', 'delivered')
          .gte('delivered_at', today);

        const { count: pendingDeliveries } = await supabase
          .from('orders')
          .select('*', { count: 'exact', head: true })
          .eq('driver_id', riderData.id)
          .in('status', ['confirmed', 'preparing', 'out_for_delivery']);

        // Calculate earnings
        const { data: earningsData } = await supabase
          .from('orders')
          .select('delivery_fee')
          .eq('driver_id', riderData.id)
          .eq('status', 'delivered');

        const totalEarnings = earningsData?.reduce((sum, order) => sum + parseFloat(String(order.delivery_fee)), 0) || 0;

        setStats({
          totalDeliveries: totalDeliveries || 0,
          todayDeliveries: todayDeliveries || 0,
          pendingDeliveries: pendingDeliveries || 0,
          rating: riderData.rating || 5.0,
          earnings: totalEarnings,
        });
      }
    } catch (error) {
      console.error('Error fetching rider data:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchAvailableOrders = async () => {
    try {
      const { data: orders } = await supabase
        .from('orders')
        .select('*')
        .eq('status', 'confirmed')
        .is('driver_id', null)
        .order('created_at', { ascending: true })
        .limit(5);

      if (orders) {
        // Fetch user profiles for each order
        const ordersWithProfiles = await Promise.all(
          orders.map(async (order) => {
            const { data: profile } = await supabase
              .from('user_profiles')
              .select('first_name, last_name, phone')
              .eq('id', order.user_id)
              .single();

            return {
              ...order,
              user_profiles: profile
            };
          })
        );

        setAvailableOrders(ordersWithProfiles);
      }
    } catch (error) {
      console.error('Error fetching available orders:', error);
    }
  };

  const toggleOnlineStatus = async () => {
    try {
      if (!user) return;

      const newStatus = isOnline ? 'offline' : 'available';
      
      const { error } = await supabase
        .from('drivers')
        .update({ status: newStatus })
        .eq('user_id', user.id);

      if (!error) {
        setIsOnline(!isOnline);
      }
    } catch (error) {
      console.error('Error updating status:', error);
    }
  };

  const acceptOrder = async (orderId: string) => {
    try {
      if (!user) return;

      // Get rider ID
      const { data: riderData } = await supabase
        .from('drivers')
        .select('id')
        .eq('user_id', user.id)
        .single();

      if (riderData) {
        const { error } = await supabase
          .from('orders')
          .update({ 
            driver_id: riderData.id,
            status: 'preparing'
          })
          .eq('id', orderId);

        if (!error) {
          Alert.alert('Success', 'Order accepted successfully!');
          fetchAvailableOrders();
          fetchRiderData();
        }
      }
    } catch (error) {
      console.error('Error accepting order:', error);
    }
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          onPress: async () => {
            await signOut();
            navigation.navigate('Welcome');
          }
        }
      ]
    );
  };

  if (loading) {
    return (
      <View className="flex-1 bg-gray-50 justify-center items-center">
        <Text className="text-lg text-gray-600">Loading...</Text>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-gray-50">
      <StatusBar style="dark" />
      
      {/* Header */}
      <View className="pt-16 pb-6 px-6 bg-white">
        <View className="flex-row items-center justify-between mb-4">
          <View>
            <Text className="text-2xl font-bold text-gray-800">Rider Dashboard</Text>
            <Text className="text-gray-600">Welcome back, {userProfile?.first_name}</Text>
          </View>
          <TouchableOpacity
            className="w-12 h-12 bg-red-600 rounded-full items-center justify-center"
            onPress={handleLogout}
          >
            <Text className="text-white text-lg">🚪</Text>
          </TouchableOpacity>
        </View>

        {/* Online Status Toggle */}
        <View className="flex-row items-center justify-between bg-gray-50 rounded-xl p-4">
          <View className="flex-row items-center">
            <View className={`w-3 h-3 rounded-full mr-3 ${isOnline ? 'bg-green-500' : 'bg-red-500'}`} />
            <Text className="text-lg font-semibold text-gray-800">
              {isOnline ? 'Online' : 'Offline'}
            </Text>
          </View>
          <Switch
            value={isOnline}
            onValueChange={toggleOnlineStatus}
            trackColor={{ false: '#f3f4f6', true: '#10b981' }}
            thumbColor={isOnline ? '#ffffff' : '#f9fafb'}
          />
        </View>
      </View>

      <ScrollView className="flex-1 px-6">
        {/* Stats Cards */}
        <View className="flex-row flex-wrap justify-between mb-6">
          <View className="w-[48%] bg-white rounded-2xl p-4 shadow-sm border border-gray-100 mb-4">
            <Text className="text-3xl mb-2">📦</Text>
            <Text className="text-2xl font-bold text-gray-800">{stats.totalDeliveries}</Text>
            <Text className="text-gray-600">Total Deliveries</Text>
          </View>
          
          <View className="w-[48%] bg-white rounded-2xl p-4 shadow-sm border border-gray-100 mb-4">
            <Text className="text-3xl mb-2">🚚</Text>
            <Text className="text-2xl font-bold text-blue-600">{stats.todayDeliveries}</Text>
            <Text className="text-gray-600">Today's Deliveries</Text>
          </View>
          
          <View className="w-[48%] bg-white rounded-2xl p-4 shadow-sm border border-gray-100 mb-4">
            <Text className="text-3xl mb-2">⭐</Text>
            <Text className="text-2xl font-bold text-yellow-600">{stats.rating.toFixed(1)}</Text>
            <Text className="text-gray-600">Rating</Text>
          </View>
          
          <View className="w-[48%] bg-white rounded-2xl p-4 shadow-sm border border-gray-100 mb-4">
            <Text className="text-3xl mb-2">⏳</Text>
            <Text className="text-2xl font-bold text-orange-600">{stats.pendingDeliveries}</Text>
            <Text className="text-gray-600">Pending</Text>
          </View>
        </View>

        {/* Earnings Card */}
        <View className="bg-emerald-600 rounded-2xl p-6 mb-6">
          <Text className="text-white text-lg mb-2">Total Earnings</Text>
          <Text className="text-white text-3xl font-bold">KSh {stats.earnings.toLocaleString()}</Text>
        </View>

        {/* Available Orders */}
        <View className="mb-6">
          <Text className="text-xl font-bold text-gray-800 mb-4">Available Orders</Text>
          {availableOrders.length === 0 ? (
            <View className="bg-white rounded-2xl p-6 items-center">
              <Text className="text-gray-500 text-lg">No orders available</Text>
              <Text className="text-gray-400 text-center mt-2">
                Turn on your online status to receive new orders
              </Text>
            </View>
          ) : (
            availableOrders.map((order) => (
              <View key={order.id} className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100 mb-4">
                <View className="flex-row justify-between items-start mb-3">
                  <View className="flex-1">
                    <Text className="font-semibold text-gray-800 text-lg">
                      {order.user_profiles?.first_name} {order.user_profiles?.last_name}
                    </Text>
                    <Text className="text-gray-600">{order.user_profiles?.phone}</Text>
                  </View>
                  <Text className="text-emerald-600 font-bold text-lg">
                    KSh {parseFloat(String(order.total_amount)).toLocaleString()}
                  </Text>
                </View>
                
                <Text className="text-gray-600 mb-4" numberOfLines={2}>
                  📍 {order.delivery_address}
                </Text>
                
                <TouchableOpacity
                  className="bg-emerald-600 py-3 px-6 rounded-xl"
                  onPress={() => acceptOrder(order.id)}
                >
                  <Text className="text-white font-semibold text-center">Accept Order</Text>
                </TouchableOpacity>
              </View>
            ))
          )}
        </View>
      </ScrollView>
    </View>
  );
};

export default RiderDashboardScreen;
