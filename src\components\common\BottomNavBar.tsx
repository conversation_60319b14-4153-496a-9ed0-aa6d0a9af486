
import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../navigation/types';

type BottomNavBarNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Home'>;

const BottomNavBar = () => {
  const navigation = useNavigation<BottomNavBarNavigationProp>();

  return (
    <View className="bg-white border-t border-gray-200 px-6 py-4">
      <View className="flex-row justify-around">
        <TouchableOpacity className="items-center">
          <Text className="text-2xl mb-1">🏠</Text>
          <Text className="text-xs text-emerald-600 font-semibold">Home</Text>
        </TouchableOpacity>
        <TouchableOpacity
          className="items-center"
          onPress={() => navigation.navigate('Cart')}
        >
          <Text className="text-2xl mb-1">🛒</Text>
          <Text className="text-xs text-gray-500">Cart</Text>
        </TouchableOpacity>
        <TouchableOpacity className="items-center">
          <Text className="text-2xl mb-1">📦</Text>
          <Text className="text-xs text-gray-500">Orders</Text>
        </TouchableOpacity>
        <TouchableOpacity
          className="items-center"
          onPress={() => navigation.navigate('Profile')}
        >
          <Text className="text-2xl mb-1">👤</Text>
          <Text className="text-xs text-gray-500">Profile</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default BottomNavBar;