import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useAuth } from '../../contexts/AuthContext';
import { supabase } from '../../lib/supabase';
import { RootStackParamList } from '../../navigation/types';

type AdminDashboardNavigationProp = NativeStackNavigationProp<RootStackParamList, 'AdminDashboard'>;

interface DashboardStats {
  totalOrders: number;
  pendingOrders: number;
  totalProducts: number;
  totalDrivers: number;
  totalRevenue: number;
}

const AdminDashboardScreen = () => {
  const navigation = useNavigation<AdminDashboardNavigationProp>();
  const { user, userProfile, signOut } = useAuth();
  const [stats, setStats] = useState<DashboardStats>({
    totalOrders: 0,
    pendingOrders: 0,
    totalProducts: 0,
    totalDrivers: 0,
    totalRevenue: 0,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Redirect if not admin
    if (userProfile && userProfile.role !== 'admin') {
      Alert.alert('Access Denied', 'You do not have admin privileges.');
      navigation.goBack();
      return;
    }

    fetchDashboardStats();
  }, [userProfile]);

  const fetchDashboardStats = async () => {
    try {
      // Fetch total orders
      const { count: ordersCount } = await supabase
        .from('orders')
        .select('*', { count: 'exact', head: true });

      // Fetch pending orders
      const { count: pendingCount } = await supabase
        .from('orders')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'pending');

      // Fetch total products
      const { count: productsCount } = await supabase
        .from('products')
        .select('*', { count: 'exact', head: true });

      // Fetch total drivers
      const { count: driversCount } = await supabase
        .from('drivers')
        .select('*', { count: 'exact', head: true });

      // Fetch total revenue
      const { data: revenueData } = await supabase
        .from('orders')
        .select('total_amount')
        .eq('payment_status', 'paid');

      const totalRevenue = revenueData?.reduce((sum, order) => sum + parseFloat(String(order.total_amount)), 0) || 0;

      setStats({
        totalOrders: ordersCount || 0,
        pendingOrders: pendingCount || 0,
        totalProducts: productsCount || 0,
        totalDrivers: driversCount || 0,
        totalRevenue,
      });
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          onPress: async () => {
            await signOut();
            navigation.navigate('Welcome');
          }
        }
      ]
    );
  };

  const menuItems = [
    { id: 1, title: 'Manage Products', icon: '📦', screen: 'ManageProducts' as keyof RootStackParamList },
    { id: 2, title: 'Manage Orders', icon: '📋', screen: 'ManageOrders' as keyof RootStackParamList },
    { id: 3, title: 'Manage Drivers', icon: '🚗', screen: 'ManageDrivers' as keyof RootStackParamList },
    { id: 4, title: 'Analytics', icon: '📊', screen: 'Analytics' as keyof RootStackParamList },
  ];

  if (loading) {
    return (
      <View className="flex-1 bg-gray-50 justify-center items-center">
        <Text className="text-lg text-gray-600">Loading...</Text>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-gray-50">
      <StatusBar style="dark" />
      
      {/* Header */}
      <View className="pt-16 pb-6 px-6 bg-white">
        <View className="flex-row items-center justify-between mb-4">
          <View>
            <Text className="text-2xl font-bold text-gray-800">Admin Dashboard</Text>
            <Text className="text-gray-600">Welcome back, {userProfile?.first_name}</Text>
          </View>
          <TouchableOpacity
            className="w-12 h-12 bg-red-600 rounded-full items-center justify-center"
            onPress={handleLogout}
          >
            <Text className="text-white text-lg">🚪</Text>
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView className="flex-1 px-6">
        {/* Stats Cards */}
        <View className="flex-row flex-wrap justify-between mb-6">
          <View className="w-[48%] bg-white rounded-2xl p-4 shadow-sm border border-gray-100 mb-4">
            <Text className="text-3xl mb-2">📋</Text>
            <Text className="text-2xl font-bold text-gray-800">{stats.totalOrders}</Text>
            <Text className="text-gray-600">Total Orders</Text>
          </View>
          
          <View className="w-[48%] bg-white rounded-2xl p-4 shadow-sm border border-gray-100 mb-4">
            <Text className="text-3xl mb-2">⏳</Text>
            <Text className="text-2xl font-bold text-orange-600">{stats.pendingOrders}</Text>
            <Text className="text-gray-600">Pending Orders</Text>
          </View>
          
          <View className="w-[48%] bg-white rounded-2xl p-4 shadow-sm border border-gray-100 mb-4">
            <Text className="text-3xl mb-2">📦</Text>
            <Text className="text-2xl font-bold text-gray-800">{stats.totalProducts}</Text>
            <Text className="text-gray-600">Products</Text>
          </View>
          
          <View className="w-[48%] bg-white rounded-2xl p-4 shadow-sm border border-gray-100 mb-4">
            <Text className="text-3xl mb-2">🚗</Text>
            <Text className="text-2xl font-bold text-gray-800">{stats.totalDrivers}</Text>
            <Text className="text-gray-600">Drivers</Text>
          </View>
        </View>

        {/* Revenue Card */}
        <View className="bg-emerald-600 rounded-2xl p-6 mb-6">
          <Text className="text-white text-lg mb-2">Total Revenue</Text>
          <Text className="text-white text-3xl font-bold">KSh {stats.totalRevenue.toLocaleString()}</Text>
        </View>

        {/* Menu Items */}
        <View className="space-y-4 mb-6">
          {menuItems.map((item) => (
            <TouchableOpacity
              key={item.id}
              className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100 flex-row items-center justify-between"
              onPress={() => navigation.navigate(item.screen)}
            >
              <View className="flex-row items-center">
                <Text className="text-2xl mr-4">{item.icon}</Text>
                <Text className="text-lg font-semibold text-gray-800">{item.title}</Text>
              </View>
              <Text className="text-gray-400 text-xl">→</Text>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </View>
  );
};

export default AdminDashboardScreen;
