declare module '@env' {
  export const SUPABASE_URL: string;
  export const SUPABASE_ANON_KEY: string;
  export const MAPBOX_ACCESS_TOKEN: string;
  export const MPESA_CONSUMER_KEY: string;
  export const MPESA_CONSUMER_SECRET: string;
  export const MPESA_PASSKEY: string;
  export const MPESA_SHORTCODE: string;
}

// Global type augmentations
declare global {
  namespace NodeJS {
    interface ProcessEnv {
      SUPABASE_URL: string;
      SUPABASE_ANON_KEY: string;
      MAPBOX_ACCESS_TOKEN: string;
      MPESA_CONSUMER_KEY: string;
      MPESA_CONSUMER_SECRET: string;
      MPESA_PASSKEY: string;
      MPESA_SHORTCODE: string;
    }
  }
}
