import React from 'react';
import { View, Text } from 'react-native';
import { StatusBar } from 'expo-status-bar';

const SplashScreen = () => {
  return (
    <View className="flex-1 bg-slate-200 items-center justify-center px-8">
      <StatusBar style="dark" />
      
      {/* Logo Container */}
      <View className="items-center mb-8">
        {/* Leaf Icon - Simple CSS version */}
        <View className="mb-4">
          <View className="w-20 h-16 bg-emerald-600 rounded-full transform rotate-45 relative">
            <View className="absolute top-2 left-2 w-12 h-1 bg-white rounded-full transform -rotate-12"></View>
          </View>
        </View>

        {/* Large J with location pin */}
        <View className="relative items-center">
          {/* J Letter */}
          <Text className="text-8xl font-bold text-emerald-600 mb-2">J</Text>

          {/* Location Pin - Simple CSS version */}
          <View className="absolute -bottom-2 left-1/2 transform -translate-x-1/2">
            <View className="w-6 h-8 bg-orange-600 rounded-t-full relative">
              <View className="absolute top-2 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-white rounded-full"></View>
            </View>
          </View>
        </View>
      </View>

      {/* Brand Text */}
      <View className="items-center">
        <Text className="text-4xl font-bold text-emerald-600 mb-2">
          JujaFresh
        </Text>
        <Text className="text-lg text-emerald-700 text-center">
          Fresh to your door. Haraka.
        </Text>
      </View>
    </View>
  );
};

export default SplashScreen;
