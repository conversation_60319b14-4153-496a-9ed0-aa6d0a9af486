import React from 'react';
import { View, Text, TouchableOpacity, Image } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/types';

type WelcomeScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Welcome'>;

const WelcomeScreen = () => {
  const navigation = useNavigation<WelcomeScreenNavigationProp>();

  return (
    <View className="flex-1 bg-white">
      <StatusBar style="dark" />
      
      {/* Header with logo */}
      <View className="items-center pt-16 pb-8">
        <View className="items-center mb-4">
          {/* Simple leaf icon */}
          <View className="w-12 h-10 bg-emerald-600 rounded-full transform rotate-45 relative mb-2">
            <View className="absolute top-1 left-1 w-8 h-0.5 bg-white rounded-full transform -rotate-12"></View>
          </View>
          <Text className="text-5xl font-bold text-emerald-600">J</Text>
        </View>
        <Text className="text-2xl font-bold text-emerald-600">JujaFresh</Text>
        <Text className="text-emerald-700 mt-1">Fresh to your door. Haraka.</Text>
      </View>

      {/* Welcome content */}
      <View className="flex-1 px-6 justify-center">
        <View className="items-center mb-12">
          <Text className="text-3xl font-bold text-gray-800 text-center mb-4">
            Welcome to JujaFresh
          </Text>
          <Text className="text-lg text-gray-600 text-center leading-6">
            Get fresh groceries and produce delivered to your doorstep in Haraka. 
            Quality ingredients, fast delivery, unbeatable convenience.
          </Text>
        </View>

        {/* Action buttons */}
        <View className="space-y-4">
          <TouchableOpacity
            className="bg-emerald-600 py-4 px-8 rounded-xl"
            onPress={() => navigation.navigate('Login')}
          >
            <Text className="text-white text-lg font-semibold text-center">
              Sign In
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            className="border-2 border-emerald-600 py-4 px-8 rounded-xl"
            onPress={() => navigation.navigate('Register')}
          >
            <Text className="text-emerald-600 text-lg font-semibold text-center">
              Create Account
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            className="py-4 px-8"
            onPress={() => navigation.navigate('Home')}
          >
            <Text className="text-gray-500 text-center">
              Continue as Guest
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Footer */}
      <View className="pb-8 px-6">
        <Text className="text-xs text-gray-400 text-center">
          By continuing, you agree to our Terms of Service and Privacy Policy
        </Text>
      </View>
    </View>
  );
};

export default WelcomeScreen;
