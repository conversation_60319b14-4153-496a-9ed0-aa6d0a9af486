
import React from 'react';
import { View, Text, TouchableOpacity, TextInput } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../navigation/types';

type HeaderNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Home'>;

interface HeaderProps {
  title?: string;
  showBackButton?: boolean;
}

const Header: React.FC<HeaderProps> = ({ title, showBackButton }) => {
  const navigation = useNavigation<HeaderNavigationProp>();

  return (
    <View className="pt-16 pb-6 px-6 bg-white">
      <View className="flex-row items-center justify-between mb-6">
        {showBackButton ? (
          <TouchableOpacity
            className="w-12 h-12 bg-gray-100 rounded-full items-center justify-center"
            onPress={() => navigation.goBack()}
          >
            <Text className="text-gray-600 text-lg">←</Text>
          </TouchableOpacity>
        ) : (
          <View>
            <Text className="text-lg text-gray-600">Find all your</Text>
            <Text className="text-2xl font-bold text-gray-800">Fresh groceries</Text>
          </View>
        )}
        <TouchableOpacity
          className="w-12 h-12 bg-emerald-600 rounded-full items-center justify-center"
          onPress={() => navigation.navigate('Profile')}
        >
          <Text className="text-white text-lg">👤</Text>
        </TouchableOpacity>
      </View>

      {title && <Text className="text-2xl font-bold text-gray-800 text-center">{title}</Text>}

      <View className="flex-row items-center bg-gray-100 rounded-xl px-4 py-4 mt-4">
        <Text className="text-gray-400 mr-3 text-lg">🔍</Text>
        <TextInput
          className="flex-1 text-base"
          placeholder="Search Store"
          placeholderTextColor="#9CA3AF"
        />
      </View>
    </View>
  );
};

export default Header;