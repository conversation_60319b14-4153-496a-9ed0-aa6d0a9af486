import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import MapView, { <PERSON><PERSON>, <PERSON>yl<PERSON>, PROVIDER_GOOGLE } from 'react-native-maps';
import * as Location from 'expo-location';

interface MapViewProps {
  showUserLocation?: boolean;
  deliveryLocation?: {
    latitude: number;
    longitude: number;
  };
  driverLocation?: {
    latitude: number;
    longitude: number;
  };
  onLocationUpdate?: (location: { latitude: number; longitude: number }) => void;
}

const CustomMapView: React.FC<MapViewProps> = ({
  showUserLocation = true,
  deliveryLocation,
  driverLocation,
  onLocationUpdate,
}) => {
  const [userLocation, setUserLocation] = useState<{
    latitude: number;
    longitude: number;
  } | null>(null);
  const [locationPermission, setLocationPermission] = useState<boolean>(false);

  useEffect(() => {
    requestLocationPermission();
  }, []);

  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status === 'granted') {
        setLocationPermission(true);
        getCurrentLocation();
      } else {
        console.log('Location permission denied');
      }
    } catch (error) {
      console.error('Error requesting location permission:', error);
    }
  };

  const getCurrentLocation = async () => {
    try {
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
      });
      
      const coords = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      };
      
      setUserLocation(coords);
      onLocationUpdate?.(coords);
    } catch (error) {
      console.error('Error getting current location:', error);
    }
  };

  if (!locationPermission) {
    return (
      <View className="flex-1 items-center justify-center bg-gray-100">
        <Text className="text-gray-600">Location permission required</Text>
      </View>
    );
  }

  return (
    <View className="flex-1">
      <MapView
        style={StyleSheet.absoluteFillObject}
        provider={PROVIDER_GOOGLE}
        showsUserLocation={showUserLocation && locationPermission}
        showsMyLocationButton={false}
        zoomEnabled={true}
        scrollEnabled={true}
        pitchEnabled={false}
        rotateEnabled={false}
        initialRegion={{
          latitude: userLocation?.latitude || -1.2921, // Default to Nairobi
          longitude: userLocation?.longitude || 36.8219,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        }}
      >
        {/* Delivery Location Marker */}
        {deliveryLocation && (
          <Marker
            coordinate={{
              latitude: deliveryLocation.latitude,
              longitude: deliveryLocation.longitude,
            }}
            title="Delivery Location"
            description="Your order will be delivered here"
          >
            <View className="w-8 h-8 bg-emerald-600 rounded-full items-center justify-center">
              <Text className="text-white text-xs">📍</Text>
            </View>
          </Marker>
        )}

        {/* Driver Location Marker */}
        {driverLocation && (
          <Marker
            coordinate={{
              latitude: driverLocation.latitude,
              longitude: driverLocation.longitude,
            }}
            title="Driver Location"
            description="Your delivery driver"
          >
            <View className="w-8 h-8 bg-orange-500 rounded-full items-center justify-center">
              <Text className="text-white text-xs">🚗</Text>
            </View>
          </Marker>
        )}

        {/* Route between driver and delivery location */}
        {driverLocation && deliveryLocation && (
          <Polyline
            coordinates={[
              {
                latitude: driverLocation.latitude,
                longitude: driverLocation.longitude,
              },
              {
                latitude: deliveryLocation.latitude,
                longitude: deliveryLocation.longitude,
              },
            ]}
            strokeColor="#059669"
            strokeWidth={3}
            lineDashPattern={[5, 5]}
          />
        )}
      </MapView>
    </View>
  );
};

export default CustomMapView;
