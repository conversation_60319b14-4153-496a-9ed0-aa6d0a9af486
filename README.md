# JujaFresh - Fresh Grocery Delivery App

JujaFresh is a React Native mobile application for fresh grocery delivery in Haraka, Kenya. Built with Expo, Supabase, and React Native Maps.

## Features

- 🛒 Browse fresh groceries by category
- 🔐 User authentication with Supabase
- 📱 Real-time order tracking with Maps
- 💳 Multiple payment methods (M-Pesa, Card, Cash)
- 🚚 Driver tracking and delivery management
- 📦 Order history and management
- ⭐ Rating and review system
- 👨‍💼 Admin dashboard for management
- 🚗 Rider dashboard for delivery drivers

## Tech Stack

- **Frontend**: React Native with Expo
- **Styling**: NativeWind (Tailwind CSS for React Native)
- **Backend**: Supabase (PostgreSQL database, Authentication, Real-time)
- **Maps**: React Native Maps for location services and tracking
- **State Management**: Zustand + React Query
- **Forms**: Formik + Yup validation
- **Navigation**: React Navigation v7

## Prerequisites

- Node.js (v18 or higher)
- npm or yarn
- Expo CLI
- Supabase account
- Google Maps API key (optional, for enhanced maps)

## Setup Instructions

### 1. Clone the repository

```bash
git clone <repository-url>
cd jujafresh-app
```

### 2. Install dependencies

```bash
npm install
```

### 3. Environment Configuration

Copy the `.env.example` file to `.env` and update with your credentials:

```bash
cp .env.example .env
```

Update the following variables in `.env`:

```env
# Supabase
SUPABASE_URL=your-supabase-url
SUPABASE_ANON_KEY=your-supabase-anon-key

# Maps (Optional)
MAPBOX_ACCESS_TOKEN=your-mapbox-access-token

# M-Pesa (Optional)
MPESA_CONSUMER_KEY=your-mpesa-consumer-key
MPESA_CONSUMER_SECRET=your-mpesa-consumer-secret
MPESA_PASSKEY=your-mpesa-passkey
MPESA_SHORTCODE=your-mpesa-shortcode
```

### 4. Supabase Setup

1. Create a new Supabase project
2. Run the SQL schema from `database/schema.sql` in your Supabase SQL editor
3. Enable Row Level Security (RLS) policies
4. Configure authentication providers if needed

### 5. Mapbox Setup

1. Create a Mapbox account
2. Get your access token from the Mapbox dashboard
3. Add the token to your `.env` file

### 6. Run the application

```bash
# Start the Expo development server
npm start

# Run on iOS simulator
npm run ios

# Run on Android emulator
npm run android

# Run on web
npm run web
```

## Project Structure

```
src/
├── components/          # Reusable UI components
│   └── MapView.tsx     # React Native Maps component
├── contexts/           # React contexts
│   └── AuthContext.tsx # Authentication context
├── hooks/              # Custom React hooks
│   └── useProducts.ts  # Product data hooks
├── lib/                # External service configurations
│   └── supabase.ts     # Supabase client and helpers
├── navigation/         # Navigation configuration
│   └── types.ts        # Navigation type definitions
├── screens/            # Screen components
│   ├── auth/           # Authentication screens
│   ├── customer/       # Customer app screens
│   └── SplashScreen.tsx
├── services/           # API services
├── store/              # State management
├── types/              # TypeScript type definitions
└── utils/              # Utility functions
```

## Database Schema

The app uses the following main tables:

- `categories` - Product categories
- `products` - Product catalog
- `user_profiles` - Extended user information
- `orders` - Customer orders
- `order_items` - Order line items
- `drivers` - Delivery drivers
- `cart_items` - Shopping cart persistence
- `reviews` - Order reviews and ratings

## Key Features Implementation

### Authentication
- Supabase Auth with email/password
- User profile management
- Row Level Security (RLS) policies

### Product Catalog
- Category-based browsing
- Real-time inventory tracking
- Product search and filtering

### Order Management
- Shopping cart persistence
- Order status tracking
- Real-time updates

### Delivery Tracking
- React Native Maps integration for live tracking
- Driver location updates
- Estimated delivery times

### Payment Integration
- M-Pesa integration (placeholder)
- Card payment support
- Cash on delivery option

## Development

### Adding New Screens

1. Create screen component in appropriate directory
2. Add to navigation types in `src/navigation/types.ts`
3. Register in `App.tsx` navigator

### Database Changes

1. Update schema in `database/schema.sql`
2. Run migrations in Supabase
3. Update TypeScript types in `src/lib/supabase.ts`

### Environment Variables

All environment variables are configured through `react-native-dotenv` and should be added to:
- `.env` file for values
- `src/types/env.d.ts` for TypeScript definitions
- `babel.config.js` for build configuration

## Deployment

### Building for Production

```bash
# Build for iOS
expo build:ios

# Build for Android
expo build:android
```

### Environment Setup

Ensure all production environment variables are properly configured before building.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support and questions, please contact the development team or create an issue in the repository.
