// Global type declarations for React Native and Expo

declare module '*.png' {
  const value: any;
  export default value;
}

declare module '*.jpg' {
  const value: any;
  export default value;
}

declare module '*.jpeg' {
  const value: any;
  export default value;
}

declare module '*.gif' {
  const value: any;
  export default value;
}

declare module '*.svg' {
  const value: any;
  export default value;
}

// Expo types augmentation
declare module 'expo-constants' {
  interface Constants {
    expoConfig?: {
      extra?: {
        [key: string]: any;
      };
    };
    manifest?: {
      [key: string]: any;
    };
    manifest2?: {
      [key: string]: any;
    };
  }
}

// React Navigation types
declare global {
  namespace ReactNavigation {
    interface RootParamList {
      Splash: undefined;
      Welcome: undefined;
      Login: undefined;
      Register: undefined;
      Home: undefined;
      Categories: undefined;
      Profile: undefined;
      Cart: undefined;
      Checkout: undefined;
      OrderTracking: { orderId: string };
      ProductDetails: { productId: string };
    }
  }
}

// Supabase types augmentation
declare module '@supabase/supabase-js' {
  interface Session {
    user: {
      id: string;
      email?: string;
      user_metadata?: {
        first_name?: string;
        last_name?: string;
        phone?: string;
        [key: string]: any;
      };
      [key: string]: any;
    };
  }
}

export {};
