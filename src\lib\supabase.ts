import { createClient } from '@supabase/supabase-js';
import { Database } from '../types/supabase';

// Fallback values for development
const supabaseUrl = process.env.SUPABASE_URL || 'https://duudqvhhsnftvsilfwru.supabase.co';
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR1dWRxdmhoc25mdHZzaWxmd3J1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTUyNTAwNTMsImV4cCI6MjA3MDgyNjA1M30.CwO4or34KhJ0e5XBQN3CF2UnPMSdwpNV_jjwOBicnm4';

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});

// Database types - using Supabase generated types
export type User = Database['public']['Tables']['user_profiles']['Row'];
export type Product = Database['public']['Tables']['products']['Row'];
export type Category = Database['public']['Tables']['categories']['Row'];
export type Order = Database['public']['Tables']['orders']['Row'];
export type OrderItem = Database['public']['Tables']['order_items']['Row'];
export type CartItem = Database['public']['Tables']['cart_items']['Row'];

// Insert types
export type UserInsert = Database['public']['Tables']['user_profiles']['Insert'];
export type ProductInsert = Database['public']['Tables']['products']['Insert'];
export type OrderInsert = Database['public']['Tables']['orders']['Insert'];
export type OrderItemInsert = Database['public']['Tables']['order_items']['Insert'];
export type CartItemInsert = Database['public']['Tables']['cart_items']['Insert'];

// Update types
export type UserUpdate = Database['public']['Tables']['user_profiles']['Update'];
export type ProductUpdate = Database['public']['Tables']['products']['Update'];
export type OrderUpdate = Database['public']['Tables']['orders']['Update'];

// Auth helper functions
export const signUp = async (email: string, password: string, userData: {
  firstName: string;
  lastName: string;
  phone: string;
}) => {
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: {
        first_name: userData.firstName,
        last_name: userData.lastName,
        phone: userData.phone,
      },
    },
  });
  
  return { data, error };
};

export const signIn = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });
  
  return { data, error };
};

export const signOut = async () => {
  const { error } = await supabase.auth.signOut();
  return { error };
};

export const getCurrentUser = async () => {
  const { data: { user }, error } = await supabase.auth.getUser();
  return { user, error };
};

// Database helper functions
export const getProducts = async (categoryId?: string) => {
  let query = supabase
    .from('products')
    .select('*, categories(name)')
    .eq('is_active', true)
    .gt('stock_quantity', 0);

  if (categoryId) {
    query = query.eq('category_id', categoryId);
  }

  const { data, error } = await query;
  return { data, error };
};

export const getCategories = async () => {
  const { data, error } = await supabase
    .from('categories')
    .select('*')
    .order('name');
    
  return { data, error };
};

export const createOrder = async (orderData: OrderInsert, items: OrderItemInsert[]) => {
  // Start a transaction
  const { data: order, error: orderError } = await supabase
    .from('orders')
    .insert(orderData)
    .select()
    .single();

  if (orderError) return { data: null, error: orderError };

  // Insert order items
  const orderItems = items.map(item => ({
    ...item,
    order_id: order.id,
  }));

  const { data: items_data, error: itemsError } = await supabase
    .from('order_items')
    .insert(orderItems);

  if (itemsError) return { data: null, error: itemsError };

  return { data: { order, items: items_data }, error: null };
};

export const getOrdersByUser = async (userId: string) => {
  const { data, error } = await supabase
    .from('orders')
    .select(`
      *,
      order_items(
        *,
        products(name, image_url)
      )
    `)
    .eq('user_id', userId)
    .order('created_at', { ascending: false });
    
  return { data, error };
};

export const updateOrderStatus = async (orderId: string, status: Order['status']) => {
  const { data, error } = await supabase
    .from('orders')
    .update({ status, updated_at: new Date().toISOString() })
    .eq('id', orderId)
    .select()
    .single();

  return { data, error };
};
